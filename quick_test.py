#!/usr/bin/env python3
"""Quick test per vedere il codice ricostruito."""

from neuroglyph.core.encoder.encoder import NGEncoder

def test_class():
    enc = NGEncoder()
    code = """class C:
    def __init__(self): 
        self.x = 0
    def add(self, a):    
        return self.x + a
    def mul(self, b):    
        return self.x * b"""
    
    print("🧪 Test classe:")
    print("Codice originale:")
    print(code)
    print("\n" + "="*50 + "\n")
    
    res = enc.round_trip_test(code, 3)
    print("Codice ricostruito:")
    print(res.reconstructed_code)
    print(f"\nSintassi valida: {res.decoding_result.reconstruction_result.syntax_valid}")

if __name__ == "__main__":
    test_class()
