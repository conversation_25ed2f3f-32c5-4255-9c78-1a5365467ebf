#!/usr/bin/env python3
"""
Mini-test per verificare le patch stabili dell'encoder.
"""

from neuroglyph.core.encoder.encoder import <PERSON>GEncoder

def test_patches():
    """Test delle patch stabili."""
    enc = NGEncoder()
    
    tests = {
        "else": """def f(x):
    if x:
        return 1
    else:
        return 0""",

        "aug": """def g():
    total = 0
    total += price * qty
    return total""",

        "class": """class Calculator:
    def __init__(self):
        self.result = 0

    def add(self, x):
        self.result += x
        return self.result"""
    }
    
    print("🧪 Test patch stabili:")
    
    for name, code in tests.items():
        print(f"\n--- Test {name} ---")
        try:
            r = enc.round_trip_test(code, 3)
            status = "OK" if r.decoding_result.reconstruction_result.syntax_valid else "FAIL"
            print(f"{name}: {status}")
            
            if status == "FAIL":
                print(f"   Errore: {r.decoding_result.reconstruction_result.error_message}")
                
        except Exception as e:
            print(f"{name}: ERROR - {e}")
    
    print("\n✅ Test completato")

if __name__ == "__main__":
    test_patches()
